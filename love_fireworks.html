<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爱心烟花</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .heart {
            position: relative;
            width: 200px;
            height: 180px;
            animation: heartbeat 1.5s ease-in-out infinite;
            z-index: 10;
        }

        .heart::before,
        .heart::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 160px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            border-radius: 50px 50px 0 0;
            transform: rotate(-45deg);
            transform-origin: 0 100%;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
        }

        .heart::after {
            left: 50px;
            transform: rotate(45deg);
            transform-origin: 100% 100%;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .firework {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            pointer-events: none;
        }

        .text {
            position: absolute;
            top: 70%;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 24px;
            text-align: center;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 10px rgba(255, 255, 255, 0.8); }
            to { text-shadow: 0 0 20px rgba(255, 255, 255, 1), 0 0 30px rgba(255, 107, 107, 0.8); }
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="heart"></div>
        <div class="text">❤️ 爱你永远 ❤️</div>
    </div>

    <script>
        // 创建星星背景
        function createStars() {
            for (let i = 0; i < 100; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 2 + 's';
                document.body.appendChild(star);
            }
        }

        // 创建烟花效果
        function createFirework(x, y) {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8'];
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                particle.style.boxShadow = `0 0 6px ${particle.style.background}`;
                
                const angle = (Math.PI * 2 * i) / particleCount;
                const velocity = Math.random() * 100 + 50;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;
                
                document.body.appendChild(particle);
                
                // 动画粒子
                let posX = x;
                let posY = y;
                let opacity = 1;
                
                const animate = () => {
                    posX += vx * 0.02;
                    posY += vy * 0.02;
                    opacity -= 0.02;
                    
                    particle.style.left = posX + 'px';
                    particle.style.top = posY + 'px';
                    particle.style.opacity = opacity;
                    
                    if (opacity > 0) {
                        requestAnimationFrame(animate);
                    } else {
                        particle.remove();
                    }
                };
                
                requestAnimationFrame(animate);
            }
        }

        // 随机烟花
        function randomFireworks() {
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            createFirework(x, y);
        }

        // 点击创建烟花
        document.addEventListener('click', (e) => {
            createFirework(e.clientX, e.clientY);
        });

        // 初始化
        createStars();
        
        // 定时创建随机烟花
        setInterval(randomFireworks, 2000);
        
        // 爱心周围的烟花
        setInterval(() => {
            const heart = document.querySelector('.heart');
            const rect = heart.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            // 在爱心周围创建烟花
            const offsetX = (Math.random() - 0.5) * 300;
            const offsetY = (Math.random() - 0.5) * 300;
            createFirework(centerX + offsetX, centerY + offsetY);
        }, 3000);
    </script>
</body>
</html>
